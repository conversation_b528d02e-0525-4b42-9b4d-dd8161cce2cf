using System;
using System.Collections.Generic;

namespace DisasterApp.Domain.Entities;

public partial class ImpactDetail
{
    public int Id { get; set; }

    public Guid ReportId { get; set; }

    public string Description { get; set; } = null!;

    public byte? Severity { get; set; }//replace string

    public bool? IsResolved { get; set; }

    public DateTime? ResolvedAt { get; set; }

    public int ImpactTypeId { get; set; }

    public virtual ImpactType ImpactType { get; set; } = null!;

    public virtual DisasterReport Report { get; set; } = null!;
}

using System;
using System.Collections.Generic;

namespace DisasterApp.Domain.Entities;

public partial class DisasterReport
{
    public Guid Id { get; set; }

    public string Title { get; set; } = null!;

    public string Description { get; set; } = null!;

    public DateTime Timestamp { get; set; }

    public byte Severity { get; set; } //string enum

    public byte Status { get; set; } //string enum

    public int? EstimatedAffected { get; set; } //remove

    public Guid? VerifiedBy { get; set; } 

    public DateTime? VerifiedAt { get; set; }

    public bool? IsDeleted { get; set; }

    public Guid UserId { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public Guid DisasterEventId { get; set; } //replace int

    public virtual DisasterEvent DisasterEvent { get; set; } = null!;

    public virtual ICollection<Donation> Donations { get; set; } = new List<Donation>();//remove

    public virtual ICollection<ImpactDetail> ImpactDetails { get; set; } = new List<ImpactDetail>();

    public virtual Location? Location { get; set; }

    public virtual ICollection<Photo> Photos { get; set; } = new List<Photo>();

    public virtual ICollection<SupportRequest> SupportRequests { get; set; } = new List<SupportRequest>();

    public virtual User User { get; set; } = null!;

    public virtual User? VerifiedByNavigation { get; set; }
}
